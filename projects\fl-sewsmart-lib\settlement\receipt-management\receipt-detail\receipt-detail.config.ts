/* 列表options */
export const listOptions = [
  { label: '收款单号', key: 'inbound_code', listKey: 'codes', type: 'select' },
  { label: '收款账户', key: 'inbound_type', listKey: 'inbound_types', type: 'select' },
  { label: '客户', key: 'style_code_ids', listKey: 'style_code', type: 'select' },
  { label: '关联单据', key: 'io_uuid', listKey: 'io_code', type: 'select' },
  { label: '关联款号', key: 'bulk_id', listKey: 'bulk_code', type: 'select' },
  { label: '币种', key: 'factory_id', listKey: 'factory', type: 'select' },
  { label: '应收日期', key: 'customer_id', listKey: '', type: 'date' },
  { label: '实收日期', key: 'inbound_time', listKey: '', type: 'date' },
  { label: '创建人', key: 'gen_user_id', listKey: 'gen_user_ids', type: 'select' },
  { label: '创建时间', key: 'gen_time', listKey: '', type: 'date' },
];

/* 收款类型选项 */
export const receiptTypeOptions = [
  { label: '预收款', value: 'advance' },
  { label: '应收款', value: 'receivable' },
];

/* 币种选项 */
export const currencyOptions = [
  { label: '人民币', value: 'CNY' },
  { label: '美元', value: 'USD' },
  { label: '欧元', value: 'EUR' },
  { label: '英镑', value: 'GBP' },
  { label: '日元', value: 'JPY' },
];

/* 表格配置 */
const headerBaseConfig = { visible: true, type: 'text', width: '112px', sort: false, pinned: false };
export const tableHeader = [
  {
    label: '收款单号',
    key: 'io_code',
    ...headerBaseConfig,
  },
  {
    label: '收款类型',
    key: 'bulk_code',
    ...headerBaseConfig,
  },
  {
    label: '收款账户',
    key: 'po_code',
    ...headerBaseConfig,
  },
  {
    label: '客户',
    key: 'style_code',
    ...headerBaseConfig,
  },
  {
    label: '关联单据',
    key: 'category',
    ...headerBaseConfig,
  },
  {
    label: '关联款号',
    key: 'color_name',
    ...headerBaseConfig,
  },
  {
    label: '收款金额 （本币）',
    key: 'spec_size',
    ...headerBaseConfig,
    width: '140px',
  },
  {
    label: '收款金额',
    key: 'stages',
    ...headerBaseConfig,
  },
  {
    label: '币种',
    key: 'due_time',
    ...headerBaseConfig,
    width: '140px',
  },
  {
    label: '应收日期',
    key: 'order_qty',
    ...headerBaseConfig,
    type: 'date',
  },
  {
    label: '实收日期',
    key: 'has_inbound_qty',
    ...headerBaseConfig,
    type: 'date',
  },
  {
    label: '创建人/创建时间',
    key: 'unit_price',
    ...headerBaseConfig,
    width: '140px',
  },
  {
    label: '状态',
    key: 'inbound_cost',
    style: getStatusColorStyle.bind(this),
    ...headerBaseConfig,
  },
];

function getStatusColorStyle(item: any) {
  const styleObj: any = {};
  switch (item.order_status) {
    case 1: // 待提交
      styleObj.color = '#138AFF';
      break;
    case 2: // 待审核
    case 9: // 修改待审核
      styleObj.color = '#FB6401';
      break;
    case 4: // 待修改
    case 10: // 修改未通过
      styleObj.color = '#FF4A1D';
      break;
    case 8: // 已取消
      styleObj.color = '#97999C';
      break;
  }
  return styleObj;
}

/* 表单配置 */
export const formConfig = [
  {
    label: '收款单号',
    key: 'receipt_code',
    type: 'input',
    required: true,
    disabled: true,
    itemSpan: 6,
    labelSpan: 6,
    controlSpan: 18,
  },
  {
    label: '收款类型',
    key: 'receipt_type',
    type: 'select',
    required: true,
    itemSpan: 6,
    labelSpan: 6,
    controlSpan: 18,
    optionKey: 'receiptTypeOptions',
    valueKey: 'value',
    labelKey: 'label',
  },
  {
    label: '收款账户',
    key: 'account_id',
    type: 'select',
    required: true,
    itemSpan: 6,
    labelSpan: 6,
    controlSpan: 18,
    optionKey: 'accountOptions',
    valueKey: 'id',
    labelKey: 'display_name',
  },
  {
    label: '收款日期',
    key: 'receipt_date',
    type: 'date',
    required: true,
    itemSpan: 6,
    labelSpan: 6,
    controlSpan: 18,
  },
  {
    label: '客户',
    key: 'customer_id',
    type: 'select',
    required: true,
    itemSpan: 6,
    labelSpan: 6,
    controlSpan: 18,
    optionKey: 'customerOptions',
    valueKey: 'id',
    labelKey: 'name',
  },
  {
    label: '币种',
    key: 'currency',
    type: 'select',
    required: true,
    itemSpan: 6,
    labelSpan: 6,
    controlSpan: 18,
    optionKey: 'currencyOptions',
    valueKey: 'value',
    labelKey: 'label',
  },
  {
    label: '汇率',
    key: 'exchange_rate',
    type: 'input-number',
    required: true,
    min: 0.0001,
    itemSpan: 6,
    labelSpan: 6,
    controlSpan: 18,
    precision: 4,
  },
  {
    label: '备注',
    key: 'remark',
    type: 'input-area',
    maxlength: 50,
    maxRows: 3,
    itemSpan: 12,
    labelSpan: 3,
    controlSpan: 21,
  },
];

/* 预收款明细表格配置 */
export const advanceDetailTableHeader = [
  {
    label: '订单号',
    key: 'order_code',
    ...headerBaseConfig,
    width: '140px',
  },
  {
    label: '品名',
    key: 'category',
    ...headerBaseConfig,
    width: '140px',
  },
  {
    label: '款号',
    key: 'style_code',
    ...headerBaseConfig,
  },
  {
    label: '品名',
    key: 'product_name',
    ...headerBaseConfig,
  },
  {
    label: '客户',
    key: 'customer_name',
    ...headerBaseConfig,
  },
  {
    label: '订单数量',
    key: 'order_quantity',
    ...headerBaseConfig,
    type: 'number',
  },
  {
    label: '暂估金额',
    key: 'estimated_amount',
    ...headerBaseConfig,
    type: 'currency',
    width: '120px',
  },
  {
    label: '预收款',
    key: 'advance_amount',
    ...headerBaseConfig,
    type: 'currency',
    width: '120px',
  },
  {
    label: '预收款（本币）',
    key: 'advance_amount_local',
    ...headerBaseConfig,
    type: 'currency',
    width: '140px',
  },
  {
    label: '已收款',
    key: 'payment',
    ...headerBaseConfig,
    type: 'currency',
    width: '120px',
    disabled: true, // 从抽屉中选中后赋值，不可修改
  },
  {
    label: '已收款（本币）',
    key: 'payment_local',
    ...headerBaseConfig,
    type: 'currency',
    width: '140px',
    disabled: true, // 从抽屉中选中后赋值，不可修改
  },
  {
    label: '本次收款比',
    key: 'current_receipt_ratio',
    ...headerBaseConfig,
    type: 'percent',
    width: '120px',
  },
  {
    label: '本次收款',
    key: 'current_receipt_amount',
    ...headerBaseConfig,
    type: 'currency',
    width: '120px',
  },
  {
    label: '本次收款（本币）',
    key: 'current_receipt_amount_local',
    ...headerBaseConfig,
    type: 'currency',
    width: '140px',
  },
  {
    label: '操作',
    key: 'action',
    ...headerBaseConfig,
    type: 'action',
    width: '80px',
  },
];

/* 应收款明细表格配置 */
export const receivableDetailTableHeader = [
  {
    label: '应收单号',
    key: 'receivable_code',
    ...headerBaseConfig,
    width: '140px',
  },
  {
    label: '出库单号',
    key: 'outbound_code',
    ...headerBaseConfig,
    width: '140px',
  },
  {
    label: '订单号',
    key: 'order_code',
    ...headerBaseConfig,
    width: '140px',
  },
  {
    label: '交付单号',
    key: 'delivery_code',
    ...headerBaseConfig,
    width: '140px',
  },
  {
    label: '款号',
    key: 'style_code',
    ...headerBaseConfig,
  },
  {
    label: '品名',
    key: 'product_name',
    ...headerBaseConfig,
  },
  {
    label: '数量',
    key: 'quantity',
    ...headerBaseConfig,
    type: 'number',
  },
  {
    label: '应收金额',
    key: 'receivable_amount',
    ...headerBaseConfig,
    type: 'currency',
    width: '120px',
  },
  {
    label: '已收金额',
    key: 'received_amount',
    ...headerBaseConfig,
    type: 'currency',
    width: '120px',
  },
  {
    label: '待收金额',
    key: 'pending_amount',
    ...headerBaseConfig,
    type: 'currency',
    width: '120px',
  },
  {
    label: '预收款',
    key: 'advance_amount',
    ...headerBaseConfig,
    type: 'currency',
    width: '120px',
  },
  {
    label: '占用预收款',
    key: 'used_advance_amount',
    ...headerBaseConfig,
    type: 'currency',
    width: '120px',
  },
  {
    label: '本次收款',
    key: 'current_receipt_amount',
    ...headerBaseConfig,
    type: 'currency',
    width: '120px',
  },
  {
    label: '本次收款（本币）',
    key: 'current_receipt_amount_local',
    ...headerBaseConfig,
    type: 'currency',
    width: '140px',
  },
  {
    label: '操作',
    key: 'action',
    ...headerBaseConfig,
    type: 'action',
    width: '80px',
  },
];
